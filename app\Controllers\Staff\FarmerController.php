<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\FarmerInformationModel;
use App\Models\districtModel;
use App\Models\llgModel;
use App\Models\wardModel;
use App\Models\FarmersChildrenModel;
use App\Models\EducationModel;

class FarmerController extends BaseController
{
    protected $farmerModel;
    protected $childrenModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;
    protected $educationModel;

    public function __construct()
    {
        helper(['form', 'url','info']);
        $this->farmerModel = new FarmerInformationModel();
        $this->childrenModel = new FarmersChildrenModel();
        $this->districtModel = new districtModel();
        $this->llgModel = new llgModel();
        $this->wardModel = new wardModel();
        $this->educationModel = new EducationModel();
    }

    public function index()
    {
        $data = [
            'title' => 'Farmers List',
            'page_header' => 'Farmers Management',
            'farmers' => $this->farmerModel->where('org_id', session()->get('org_id'))->where('district_id', session()->get('district_id'))->findAll()
        ];

        return view('staff/farmers/index', $data);
    }

    public function create()
    {
        // Get LLGs for the district from session
        $districtId = session()->get('district_id');
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();
        
        // Get district name
        $district = $this->districtModel->find($districtId);
        $districtName = $district ? $district['name'] : 'No District Assigned';

        $data = [
            'title' => 'Add New Farmer',
            'page_header' => 'Add New Farmer',
            'llgs' => $llgs,
            'district_name' => $districtName,
            'education_levels' => $this->educationModel->findAll()
        ];

        return view('staff/farmers/create', $data);
    }

    public function store()
    {
        $data = $this->request->getPost();
        $data['org_id'] = session()->get('org_id');
        $data['created_by'] = session()->get('emp_id');
        $data['province_id'] = session()->get('orgprovince_id');
        $data['district_id'] = session()->get('district_id'); // Ensure district_id is set from session

        // Handle photo upload
        $photo = $this->request->getFile('id_photo');
        if ($photo && $photo->isValid() && !$photo->hasMoved()) {
            $newName = $photo->getRandomName();
            $photo->move(FCPATH . 'public/uploads/farmer_photos', $newName);
            $data['id_photo'] = 'public/uploads/farmer_photos/' . $newName;
        }

        if ($this->farmerModel->insert($data)) {
            return redirect()->to('staff/farmers')->with('success', 'Farmer added successfully');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to add farmer');
    }

    public function edit($id)
    {
        $farmer = $this->farmerModel->find($id);
        
        if (!$farmer || $farmer['org_id'] != session()->get('org_id')) {
            return redirect()->to('staff/farmers')->with('error', 'Farmer not found');
        }

        // Get district name
        $district = $this->districtModel->find(session()->get('district_id'));
        $districtName = $district ? $district['name'] : 'No District Assigned';
        
        // Get LLGs for the current district
        $llgs = $this->llgModel->where('district_id', session()->get('district_id'))->findAll();
        
        // Get Wards for the current LLG
        $wards = [];
        if ($farmer['llg_id']) {
            $wards = $this->wardModel->where('llg_id', $farmer['llg_id'])->findAll();
        }

        $data = [
            'title' => 'Edit Farmer',
            'page_header' => 'Edit Farmer',
            'farmer' => $farmer,
            'children' => $this->childrenModel->where('farmer_id', $id)->findAll(),
            'district_name' => $districtName,
            'llgs' => $llgs,
            'wards' => $wards,
            'education_levels' => $this->educationModel->findAll()
        ];

        return view('staff/farmers/edit', $data);
    }

    public function update($id)
    {
        $data = $this->request->getPost();
        $data['updated_by'] = session()->get('emp_id');
        $data['province_id'] = session()->get('orgprovince_id');

        // Handle photo upload
        $photo = $this->request->getFile('id_photo');
        if ($photo && $photo->isValid() && !$photo->hasMoved()) {
            // Delete old photo if exists
            $farmer = $this->farmerModel->find($id);
            if ($farmer['id_photo'] && file_exists(FCPATH . $farmer['id_photo'])) {
                unlink(FCPATH . $farmer['id_photo']);
            }

            $newName = $photo->getRandomName();
            $photo->move(FCPATH . 'public/uploads/farmer_photos', $newName);
            $data['id_photo'] = 'public/uploads/farmer_photos/'. $newName;
        }

        if ($this->farmerModel->update($id, $data)) {
            return redirect()->back()->with('success', 'Farmer updated successfully');
        }

        return redirect()->back()->withInput()->with('error', 'Failed to update farmer');
    }

    public function delete($id)
    {
        $farmer = $this->farmerModel->find($id);
        
        if (!$farmer || $farmer['org_id'] != session()->get('org_id')) {
            return redirect()->to('staff/farmers')->with('error', 'Farmer not found');
        }

        if ($this->farmerModel->delete($id)) {
            return redirect()->to('staff/farmers')->with('success', 'Farmer deleted successfully');
        }

        return redirect()->back()->with('error', 'Failed to delete farmer');
    }

    public function view($id)
    {
        $farmer = $this->farmerModel->find($id);
        
        if (!$farmer || $farmer['org_id'] != session()->get('org_id')) {
            return redirect()->to('staff/farmers')->with('error', 'Farmer not found');
        }

        $data = [
            'title' => 'Farmer Details',
            'page_header' => 'Farmer Details',
            'farmer' => $farmer,
            'children' => $this->childrenModel->where('farmer_id', $id)->findAll()
        ];

        return view('staff/farmers/view', $data);
    }

    public function getLLGs()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $districtId = $this->request->getPost('district_id');
        if (!$districtId) {
            return $this->response->setJSON(['success' => false, 'message' => 'District ID is required']);
        }

        try {
            $llgs = $this->llgModel
                ->where('district_id', $districtId)
                ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'llgs' => $llgs
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error in getLLGs: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Database error occurred'
            ]);
        }
    }

    public function getWards()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $llgId = $this->request->getGet('llg_id');
        if (!$llgId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'LLG ID is required'
            ]);
        }

        try {
            $wards = $this->wardModel
                ->where('llg_id', $llgId)
                ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'wards' => $wards
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error in getWards: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Database error occurred'
            ]);
        }
    }

    public function getLlgsByDistrict($districtId)
    {
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();
        return $this->response->setJSON($llgs);
    }

    public function getWardsByLlg($llgId)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid request']);
        }

        try {
            $wards = $this->wardModel
                ->where('llg_id', $llgId)
                ->findAll();

            return $this->response->setJSON($wards);
        } catch (\Exception $e) {
            log_message('error', 'Error in getWardsByLlg: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Server error']);
        }
    }

    public function addChild()
    {
        $data = $this->request->getPost();
        
        if ($this->childrenModel->insert($data)) {
            return redirect()->back()->with('success', 'Child added successfully');
        }
        
        return redirect()->back()->withInput()->with('error', 'Failed to add child');
    }

    public function updateChild()
    {
        $id = $this->request->getPost('id');
        $data = $this->request->getPost();
        $data['updated_by'] = session()->get('emp_id');
        if ($this->childrenModel->update($id, $data)) {
            return redirect()->back()->with('success', 'Child updated successfully');
        }
        
        return redirect()->back()->withInput()->with('error', 'Failed to update child');
    }

    public function deleteChild($id)
    {
        if ($this->childrenModel->delete($id)) {
            return redirect()->back()->with('success', 'Child deleted successfully');
        }
        
        return redirect()->back()->with('error', 'Failed to delete child');
    }
}
