<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;

class StaffController extends BaseController
{
    public function __construct()
    {
        // Load the info helper to make imgcheck() and other helper functions available
        helper('info');
    }
    
    public function index()
    {
        $exerciseModel = new \App\Models\ExerciseModel();
        
        // Get exercises with district name and officer name
        $exercises = $exerciseModel->select('exercises.*, adx_district.name as district_name, users.name as officer_name')
            ->join('adx_district', 'adx_district.id = exercises.district_id', 'left')
            ->join('users', 'users.id = exercises.officer_responsible_id', 'left')
            ->findAll();
        
        $data = [
            'title' => 'Staff Dashboard',
            'exercises' => $exercises
        ];
        
        return view('staff/staff_dashboard', $data);
    }
} 