<?php

namespace App\Controllers;

use App\Models\districtModel;
use App\Models\employeesModel;
use App\Models\orgModel;
use App\Models\PermissionsUserDistrictsModel;
use App\Models\projectsModel;
use App\Models\provinceModel;
use App\Models\UsersModel;

class Home extends BaseController
{
    public $session;
    public $UsersModel;
    public $orgModel;
    public $provinceModel;
    public $districtModel;
    public $districtPermissionModel;



    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();

        $this->UsersModel = new UsersModel();
        $this->orgModel = new orgModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new districtModel();
        $this->districtPermissionModel = new PermissionsUserDistrictsModel();
    }

    public function index()
    {
        $data['title'] = "Home";
        $data['menu'] = "home";

        echo view('home/home', $data);
    }

    public function about()
    {
        $data['title'] = "About Org.Calendar";
        $data['menu'] = "about";
        echo view('home/about', $data);
    }


   

    public function login()
    {
        // Display login form with updated label for Fileno/Email
        $data['title'] = "Login";
        $data['menu'] = "login";
        // Use the home_login view
        echo view('home/home_login', $data);
    }
    
    public function processLogin()
    {
        // Validate form data
        $rules = [
            'identifier' => 'required',
            'password' => 'required'
        ];
        
        if (!$this->validate($rules)) {
            // Redirect to login page with error message instead of using back()
            return redirect()->to('login')->with('error', 'Please enter both identifier and password');
        }

        // Retrieve form data
        $identifier = $this->request->getVar('identifier');
        $password = $this->request->getVar('password');

        // DEBUG: Display user information
        $debug = $this->request->getVar('debug');
        if ($debug === 'true') {
            $user = $this->UsersModel->where('fileno', $identifier)
                                     ->orWhere('email', $identifier)
                                     ->first();
            
            if ($user) {
                echo "<h3>User Found:</h3>";
                echo "<pre>";
                print_r($user);
                echo "</pre>";
                
                echo "<h3>Password Check:</h3>";
                $pwCheck = password_verify($password, $user['password']);
                echo "Password verification result: " . ($pwCheck ? "TRUE" : "FALSE");
                
                echo "<h3>Organization Check:</h3>";
                echo "Has org_id: " . (isset($user['org_id']) ? "YES" : "NO") . "<br>";
                if (isset($user['org_id'])) {
                    $org = $this->orgModel->where('id', $user['org_id'])->first();
                    echo "<pre>";
                    print_r($org);
                    echo "</pre>";
                }
                
                echo "<h3>Active Status Check:</h3>";
                echo "Has status: " . (isset($user['status']) ? "YES" : "NO") . "<br>";
                if (isset($user['status'])) {
                    echo "status value: " . $user['status'];
                }
                
                exit;
            } else {
                echo "No user found with identifier: " . htmlspecialchars($identifier);
                exit;
            }
        }

        // Check if user exists by fileno or email
        $user = $this->UsersModel->where('fileno', $identifier)
                                 ->orWhere('email', $identifier)
                                 ->first();
        
        // If user not found, return error
        if (!$user) {
            return redirect()->to('login')->with('error', 'Incorrect Fileno/Email or Password!');
        }

        // Check if user is associated with an organization
        if (empty($user['org_id'])) {
            return redirect()->to('login')->with('error', 'Username is not associated with any organization!');
        }

        // Check if password is correct
        if (!password_verify($password, $user['password'])) {
            return redirect()->to('login')->with('error', 'Incorrect Fileno/Email or Password!');
        }

        // Get organization information
        $org = $this->orgModel->where('id', $user['org_id'])->first();
        
        // Check if organization is active
        if ($org['is_active'] != 1) {
            return redirect()->to('login')->with('error', 'Your Organization has been Deactivated!');
        }
        
        // Check if user account is active
        if ($user['status'] != 1) {
            return redirect()->to('login')->with('error', 'Your account is not active!');
        }

        // Get default district
        $districtPermission = $this->districtPermissionModel
            ->where('user_id', $user['id'])
            ->where('org_id', $user['org_id'])
            ->where('default_district', 1)
            ->first();

        // Get all assigned districts
        $allDistrictPermissions = $this->districtPermissionModel
            ->where('user_id', $user['id'])
            ->where('org_id', $user['org_id'])
            ->findAll();
        $assignedDistricts = [];
        
        foreach($allDistrictPermissions as $permission) {
            $district = $this->districtModel->where('id', $permission['district_id'])->first();
            if($district) {
                $assignedDistricts[] = [
                    'id' => $district['id'],
                    'name' => $district['name'],
                    'is_default' => $permission['default_district'] == 1
                ];
            }
        }
        
        // Set session data
        $this->session->set('assigned_districts', $assignedDistricts);

        // Set default district if available
        if($districtPermission){
            $district = $this->districtModel->where('id', $districtPermission['district_id'])->first();
            if($district) {
                $this->session->set('district_id', $district['id']);
                $this->session->set('district_name', $district['name']);
            } else {
                // If default district not found, try to use the first available district
                if(!empty($assignedDistricts)) {
                    $this->session->set('district_id', $assignedDistricts[0]['id']);
                    $this->session->set('district_name', $assignedDistricts[0]['name']);
                } else {
                    // No districts assigned
                    $this->session->set('district_id', null);
                    $this->session->set('district_name', 'No District Assigned');
                }
            }
        } else {
            // No default district set, try to use the first available district
            if(!empty($assignedDistricts)) {
                $this->session->set('district_id', $assignedDistricts[0]['id']);
                $this->session->set('district_name', $assignedDistricts[0]['name']);
            } else {
                // No districts assigned
                $this->session->set('district_id', null);
                $this->session->set('district_name', 'No District Assigned');
            }
        }

        // Store user data in session
        $this->session->set('emp_id', $user['id']);
        $this->session->set('fileno', $user['fileno']);
        $this->session->set('name', $user['name']);
        $this->session->set('role', $user['role']);
        $this->session->set('position', $user['position']);
        $this->session->set('status', $user['status']);
        $this->session->set('orgname', $org['name']);
        $this->session->set('orglogo', $org['orglogo']); 
        $this->session->set('orgcode', $org['orgcode']);
        $this->session->set('org_id', $org['id']);
        $this->session->set('orgcountry_id', $org['addlockcountry']);
        $this->session->set('orgprovince_id', $org['addlockprov']);
        $this->session->set('logged_in', true);
        
        //get province
        $province = $this->provinceModel->where('id', $org['addlockprov'])->first();
        $this->session->set('province_json_id', $province['json_id']);

        // Redirect based on user role
        if ($user['role'] == 'admin' || $user['role'] == 'supervisor' || $user['role'] == 'guest') {
            // Redirect to admin dashboard
            return redirect()->to('dashboard');
        } else {
            // Redirect to staff dashboard
            return redirect()->to('staff');
        }
    }

    public function logout()
    {
        // Destroy the user's session
        $session = session();
        $session->destroy();

        // Redirect to the login page
        return redirect()->to(base_url());
    }
}
