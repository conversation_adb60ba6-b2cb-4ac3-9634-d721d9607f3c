<?php

namespace App\Controllers\Dashboards;

use App\Controllers\BaseController;
use App\Models\ProvinceModel;
use App\Models\DistrictModel;
use App\Models\LlgModel;
use App\Models\WardModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel;
use App\Models\CropsModel;
use App\Models\CropsFarmCropsDataModel;
use App\Models\CropsFarmDiseaseDataModel;
use App\Models\InfectionsModel;
use App\Models\LivestockModel;
use App\Models\LivestockFarmDataModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\FertilizersModel;
use App\Models\CropsFarmFertilizerDataModel;
use App\Models\PesticidesModel;
use App\Models\CropsFarmPesticidesDataModel;
use App\Models\CropsFarmMarketingDataModel;

class Location_dashboard extends BaseController
{
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;
    protected $farmerModel;
    protected $cropsFarmBlockModel;
    protected $cropsModel;
    protected $cropsFarmCropsDataModel;
    protected $cropsFarmDiseaseDataModel;
    protected $infectionsModel;
    protected $livestockModel;
    protected $livestockFarmDataModel;
    protected $livestockFarmBlockModel;
    protected $fertilizersModel;
    protected $cropsFarmFertilizerDataModel;
    protected $pesticidesModel;
    protected $cropsFarmPesticidesDataModel;
    protected $cropsFarmMarketingDataModel;

    public function __construct()
    {
        $this->provinceModel = new ProvinceModel();
        $this->districtModel = new DistrictModel();
        $this->llgModel = new LlgModel();
        $this->wardModel = new WardModel();
        $this->farmerModel = new FarmerInformationModel();
        $this->cropsFarmBlockModel = new CropsFarmBlockModel();
        $this->cropsModel = new CropsModel();
        $this->cropsFarmCropsDataModel = new CropsFarmCropsDataModel();
        $this->cropsFarmDiseaseDataModel = new CropsFarmDiseaseDataModel();
        $this->infectionsModel = new InfectionsModel();
        $this->livestockModel = new LivestockModel();
        $this->livestockFarmDataModel = new LivestockFarmDataModel();
        $this->livestockFarmBlockModel = new LivestockFarmBlockModel();
        $this->fertilizersModel = new FertilizersModel();
        $this->cropsFarmFertilizerDataModel = new CropsFarmFertilizerDataModel();
        $this->pesticidesModel = new PesticidesModel();
        $this->cropsFarmPesticidesDataModel = new CropsFarmPesticidesDataModel();
        $this->cropsFarmMarketingDataModel = new CropsFarmMarketingDataModel();
        helper(['form', 'url', 'info', 'array']);
    }

    public function index()
    {
        $data = [
            'title' => 'Location Dashboard',
            'menu' => 'location-dashboard',
            'provinces' => $this->provinceModel->findAll()
        ];
        
        return view('dashboard_reports/dashboard_locations', $data);
    }

    public function getDistricts()
    {
        $provinceId = $this->request->getPost('province_id');
        $districts = $this->districtModel->where('province_id', $provinceId)->findAll();
        return $this->response->setJSON($districts);
    }

    public function getLlgs()
    {
        $districtId = $this->request->getPost('district_id');
        $llgs = $this->llgModel->where('district_id', $districtId)->findAll();
        return $this->response->setJSON($llgs);
    }

    public function getWards()
    {
        $llgId = $this->request->getPost('llg_id');
        $wards = $this->wardModel->where('llg_id', $llgId)->findAll();
        return $this->response->setJSON($wards);
    }

    public function getDashboardFarmersData()
    {
        try {
            $conditions = $this->getBaseConditions();
            $data = $this->getFarmersData($conditions);
            return $this->response->setJSON([
                'error' => false,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Farmers Data Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'error' => true,
                'data' => [
                    'total' => 0,
                    'gender_distribution' => [],
                    'age_distribution' => [],
                    'education_distribution' => []
                ]
            ]);
        }
    }

    public function getDashboardCropsData()
    {
        try {
            $conditions = $this->getBaseConditions();
            $data = $this->getCropsData($conditions);
            return $this->response->setJSON([
                'error' => false,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Crops Data Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'error' => true,
                'data' => [
                    'total_blocks' => 0,
                    'hectares_info' => ['total_hectares' => 0, 'removed_hectares' => 0],
                    'disease_info' => ['diseased_hectares' => 0],
                    'crop_types' => []
                ]
            ]);
        }
    }

    public function getDashboardLivestockData()
    {
        try {
            $conditions = $this->getBaseConditions();
            log_message('debug', 'Livestock conditions: ' . json_encode($conditions));
            
            $data = $this->getLivestockData($conditions);
            log_message('debug', 'Livestock data: ' . json_encode($data));
            
            return $this->response->setJSON([
                'error' => false,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Livestock Data Error: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return $this->response->setJSON([
                'error' => true,
                'data' => [
                    'total_blocks' => 0,
                    'livestock_data' => []
                ]
            ]);
        }
    }

    public function getDashboardMarketData()
    {
        try {
            $conditions = $this->getBaseConditions();
            $data = $this->getCropsMarketData($conditions);
            return $this->response->setJSON([
                'error' => false,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Market Data Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'error' => true,
                'data' => []
            ]);
        }
    }

    public function getDashboardFertilizerData()
    {
        try {
            $conditions = $this->getBaseConditions();
            $data = $this->getFertilizersData($conditions);
            return $this->response->setJSON([
                'error' => false,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Fertilizer Data Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'error' => true,
                'data' => []
            ]);
        }
    }

    public function getDashboardPesticideData()
    {
        try {
            $conditions = $this->getBaseConditions();
            $data = $this->getPesticidesData($conditions);
            return $this->response->setJSON([
                'error' => false,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Pesticide Data Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'error' => true,
                'data' => []
            ]);
        }
    }

    public function getDashboardLivestockMarketData()
    {
        try {
            $conditions = $this->getBaseConditions();
            $data = $this->getLivestockMarketData($conditions);
            return $this->response->setJSON([
                'error' => false,
                'data' => $data
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Livestock Market Data Error: ' . $e->getMessage());
            return $this->response->setJSON([
                'error' => true,
                'data' => []
            ]);
        }
    }

    private function getBaseConditions()
    {
        $conditions = [];
        
        $province_id = $this->request->getPost('province_id');
        $district_id = $this->request->getPost('district_id');
        $llg_id = $this->request->getPost('llg_id');
        $ward_id = $this->request->getPost('ward_id');

        if (!empty($province_id)) {
            $conditions['province_id'] = $province_id;
        }
        if (!empty($district_id)) {
            $conditions['district_id'] = $district_id;
        }
        if (!empty($llg_id)) {
            $conditions['llg_id'] = $llg_id;
        }
        if (!empty($ward_id)) {
            $conditions['ward_id'] = $ward_id;
        }

        $conditions['created_at >='] = date('Y-m-d H:i:s', strtotime('-12 months'));
        return $conditions;
    }

    private function getFarmersData($conditions)
    {
        $builder = $this->farmerModel->builder();
        $tableName = $builder->getTable();
        
        // Apply date filter
        if (isset($conditions['created_at >='])) {
            $builder->where($tableName . '.created_at >=', $conditions['created_at >=']);
        }
        
        // Apply location filters progressively
        if (isset($conditions['province_id']) && $conditions['province_id'] !== '') {
            $builder->where($tableName . '.province_id', $conditions['province_id']);
        }
        if (isset($conditions['district_id']) && $conditions['district_id'] !== '') {
            $builder->where($tableName . '.district_id', $conditions['district_id']);
        }
        if (isset($conditions['llg_id']) && $conditions['llg_id'] !== '') {
            $builder->where($tableName . '.llg_id', $conditions['llg_id']);
        }
        if (isset($conditions['ward_id']) && $conditions['ward_id'] !== '') {
            $builder->where($tableName . '.ward_id', $conditions['ward_id']);
        }
        
        // Get total farmers
        $totalFarmers = $builder->countAllResults(false);
        
        // Gender distribution
        $genderDist = $builder->select($tableName . '.gender, COUNT(*) as count')
                            ->groupBy($tableName . '.gender')
                            ->get()->getResultArray();

        // Age distribution with null handling
        $ageDist = $builder->select("
            CASE 
                WHEN " . $tableName . ".date_of_birth IS NULL THEN 'Unknown'
                WHEN " . $tableName . ".date_of_birth = '0000-00-00' THEN 'Unknown'
                WHEN TIMESTAMPDIFF(YEAR, " . $tableName . ".date_of_birth, CURDATE()) < 18 THEN 'Under 18'
                WHEN TIMESTAMPDIFF(YEAR, " . $tableName . ".date_of_birth, CURDATE()) BETWEEN 18 AND 30 THEN '18-30'
                WHEN TIMESTAMPDIFF(YEAR, " . $tableName . ".date_of_birth, CURDATE()) BETWEEN 31 AND 50 THEN '31-50'
                WHEN TIMESTAMPDIFF(YEAR, " . $tableName . ".date_of_birth, CURDATE()) BETWEEN 51 AND 80 THEN '51-80'
                ELSE 'Over 80'
            END as age_group,
            COUNT(*) as count")
            ->where($tableName . '.status', 'active');

        // Apply location filters for age distribution
        if (isset($conditions['province_id']) && $conditions['province_id'] !== '') {
            $builder->where($tableName . '.province_id', $conditions['province_id']);
        }
        if (isset($conditions['district_id']) && $conditions['district_id'] !== '') {
            $builder->where($tableName . '.district_id', $conditions['district_id']);
        }
        if (isset($conditions['llg_id']) && $conditions['llg_id'] !== '') {
            $builder->where($tableName . '.llg_id', $conditions['llg_id']);
        }
        if (isset($conditions['ward_id']) && $conditions['ward_id'] !== '') {
            $builder->where($tableName . '.ward_id', $conditions['ward_id']);
        }

        $ageDist = $builder->groupBy('age_group')
            ->get()->getResultArray();

        // Education distribution with location filters
        $eduDist = $builder->select($tableName . '.highest_education_id, COUNT(*) as count')
                        ->where($tableName . '.status', 'active');

        // Apply location filters for education distribution
        if (isset($conditions['province_id']) && $conditions['province_id'] !== '') {
            $builder->where($tableName . '.province_id', $conditions['province_id']);
        }
        if (isset($conditions['district_id']) && $conditions['district_id'] !== '') {
            $builder->where($tableName . '.district_id', $conditions['district_id']);
        }
        if (isset($conditions['llg_id']) && $conditions['llg_id'] !== '') {
            $builder->where($tableName . '.llg_id', $conditions['llg_id']);
        }
        if (isset($conditions['ward_id']) && $conditions['ward_id'] !== '') {
            $builder->where($tableName . '.ward_id', $conditions['ward_id']);
        }

        $eduDist = $builder->groupBy($tableName . '.highest_education_id')
                        ->get()->getResultArray();

        return [
            'total' => $totalFarmers,
            'gender_distribution' => $genderDist,
            'age_distribution' => $ageDist,
            'education_distribution' => $eduDist
        ];
    }

    private function getCropsData($conditions)
    {
        try {
            // Get total blocks and crop types
            $blockBuilder = $this->cropsFarmBlockModel->builder();
            $blockBuilder->select('
                crops_farm_blocks.crop_id,
                adx_crops.crop_name,
                adx_crops.crop_color_code,
                COUNT(*) as count
            ')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
            ->where('crops_farm_blocks.status', 'active');

            // Apply location filters
            if (!empty($conditions['province_id'])) {
                $blockBuilder->where('crops_farm_blocks.province_id', $conditions['province_id']);
            }
            if (!empty($conditions['district_id'])) {
                $blockBuilder->where('crops_farm_blocks.district_id', $conditions['district_id']);
            }
            if (!empty($conditions['llg_id'])) {
                $blockBuilder->where('crops_farm_blocks.llg_id', $conditions['llg_id']);
            }
            if (!empty($conditions['ward_id'])) {
                $blockBuilder->where('crops_farm_blocks.ward_id', $conditions['ward_id']);
            }

            $blockBuilder->groupBy('crops_farm_blocks.crop_id, adx_crops.crop_name');
            $cropTypes = $blockBuilder->get()->getResultArray();

            // Calculate total blocks
            $totalBlocks = array_sum(array_column($cropTypes, 'count'));

            // Get crops data
            $cropsBuilder = $this->cropsFarmCropsDataModel->builder();
            $cropsBuilder->select('
                SUM(CASE WHEN action_type = "add" THEN hectares ELSE 0 END) as total_hectares,
                SUM(CASE WHEN action_type = "remove" THEN hectares ELSE 0 END) as removed_hectares
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_crops_data.block_id')
            ->where('crops_farm_crops_data.status', 'active')
            ->where('crops_farm_blocks.status', 'active');

            // Apply filters
            if (!empty($conditions['created_at >='])) {
                $cropsBuilder->where('crops_farm_crops_data.created_at >=', $conditions['created_at >=']);
            }
            if (!empty($conditions['province_id'])) {
                $cropsBuilder->where('crops_farm_blocks.province_id', $conditions['province_id']);
            }
            if (!empty($conditions['district_id'])) {
                $cropsBuilder->where('crops_farm_blocks.district_id', $conditions['district_id']);
            }
            if (!empty($conditions['llg_id'])) {
                $cropsBuilder->where('crops_farm_blocks.llg_id', $conditions['llg_id']);
            }
            if (!empty($conditions['ward_id'])) {
                $cropsBuilder->where('crops_farm_blocks.ward_id', $conditions['ward_id']);
            }

            $hectaresInfo = $cropsBuilder->get()->getRowArray() ?? [
                'total_hectares' => 0,
                'removed_hectares' => 0
            ];

            // Get disease data
            $diseaseBuilder = $this->cropsFarmDiseaseDataModel->builder();
            $diseaseBuilder->select('COALESCE(SUM(hectares), 0) as diseased_hectares')
                ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_disease_data.block_id')
                ->where('crops_farm_disease_data.status', 'active')
                ->where('crops_farm_blocks.status', 'active');

            // Apply filters to disease data
            if (!empty($conditions['created_at >='])) {
                $diseaseBuilder->where('crops_farm_disease_data.created_at >=', $conditions['created_at >=']);
            }
            if (!empty($conditions['province_id'])) {
                $diseaseBuilder->where('crops_farm_blocks.province_id', $conditions['province_id']);
            }
            if (!empty($conditions['district_id'])) {
                $diseaseBuilder->where('crops_farm_blocks.district_id', $conditions['district_id']);
            }
            if (!empty($conditions['llg_id'])) {
                $diseaseBuilder->where('crops_farm_blocks.llg_id', $conditions['llg_id']);
            }
            if (!empty($conditions['ward_id'])) {
                $diseaseBuilder->where('crops_farm_blocks.ward_id', $conditions['ward_id']);
            }

            $diseaseInfo = $diseaseBuilder->get()->getRowArray() ?? ['diseased_hectares' => 0];

            // Return data in the expected structure
            return [
                'total_blocks' => (int)$totalBlocks,
                'hectares_info' => [
                    'total_hectares' => (float)($hectaresInfo['total_hectares'] ?? 0),
                    'removed_hectares' => (float)($hectaresInfo['removed_hectares'] ?? 0)
                ],
                'disease_info' => [
                    'diseased_hectares' => (float)($diseaseInfo['diseased_hectares'] ?? 0)
                ],
                'crop_types' => $cropTypes
            ];

        } catch (\Exception $e) {
            log_message('error', 'Error in getCropsData: ' . $e->getMessage());
            
            // Return default values in the expected structure
            return [
                'total_blocks' => 0,
                'hectares_info' => [
                    'total_hectares' => 0,
                    'removed_hectares' => 0
                ],
                'disease_info' => [
                    'diseased_hectares' => 0
                ],
                'crop_types' => []
            ];
        }
    }

    private function getLivestockData($conditions)
    {
        try {
            // 1. Get total blocks count
            $blockQuery = $this->livestockFarmBlockModel
                ->where('status', 'active');

            // Apply location filters to blocks count
            if (!empty($conditions['province_id'])) {
                $blockQuery->where('province_id', $conditions['province_id']);
            }
            if (!empty($conditions['district_id'])) {
                $blockQuery->where('district_id', $conditions['district_id']);
            }
            if (!empty($conditions['llg_id'])) {
                $blockQuery->where('llg_id', $conditions['llg_id']);
            }
            if (!empty($conditions['ward_id'])) {
                $blockQuery->where('ward_id', $conditions['ward_id']);
            }

            $totalBlocks = $blockQuery->countAllResults();

            // Get livestock summary using the model method
            $summaryConditions = [];
            
            // Apply location filters
            if (!empty($conditions['province_id'])) {
                $summaryConditions['livestock_farm_blocks.province_id'] = $conditions['province_id'];
            }
            if (!empty($conditions['district_id'])) {
                $summaryConditions['livestock_farm_blocks.district_id'] = $conditions['district_id'];
            }
            if (!empty($conditions['llg_id'])) {
                $summaryConditions['livestock_farm_blocks.llg_id'] = $conditions['llg_id'];
            }
            if (!empty($conditions['ward_id'])) {
                $summaryConditions['livestock_farm_blocks.ward_id'] = $conditions['ward_id'];
            }

            // Apply date filter if exists
            if (isset($conditions['created_at >='])) {
                $summaryConditions['livestock_farm_data.created_at >='] = $conditions['created_at >='];
            }

            $livestockData = $this->livestockFarmDataModel->getLivestockSummary($summaryConditions);

            // Format the data for the dashboard
            $finalData = array_map(function($item) {
                return [
                    'livestock_id' => $item['livestock_id'],
                    'livestock_name' => $item['livestock_name'],
                    'livestock_color_code' => $item['livestock_color_code'],
                    'male_count' => (int)$item['total_male'],
                    'female_count' => (int)$item['total_female'],
                    'total_count' => (int)$item['total_male'] + (int)$item['total_female']
                ];
            }, $livestockData);

            // Filter out livestock with zero counts
            $finalData = array_values(array_filter($finalData, function($item) {
                return $item['total_count'] > 0;
            }));

            return [
                'total_blocks' => (int)$totalBlocks,
                'livestock_data' => $finalData
            ];

        } catch (\Exception $e) {
            log_message('error', 'Error in getLivestockData: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return [
                'total_blocks' => 0,
                'livestock_data' => []
            ];
        }
    }

    private function getCropsMarketData($conditions)
    {
        try {
            // Get market summary by crop
            $marketConditions = [
                'crops_farm_marketing_data.status' => 'active',
                'farmer_information.status' => 'active'
            ];

            // Apply date filter if exists
            if (isset($conditions['created_at >='])) {
                $marketConditions['crops_farm_marketing_data.created_at >='] = $conditions['created_at >='];
            }

            // Apply location filters
            if (!empty($conditions['province_id'])) {
                $marketConditions['farmer_information.province_id'] = $conditions['province_id'];
            }
            if (!empty($conditions['district_id'])) {
                $marketConditions['farmer_information.district_id'] = $conditions['district_id'];
            }
            if (!empty($conditions['llg_id'])) {
                $marketConditions['farmer_information.llg_id'] = $conditions['llg_id'];
            }
            if (!empty($conditions['ward_id'])) {
                $marketConditions['farmer_information.ward_id'] = $conditions['ward_id'];
            }

            // Get market data by crop
            $cropData = $this->cropsFarmMarketingDataModel->getMarketSummaryByCrop($marketConditions);

            // Get market data by buyer
            $buyerData = $this->cropsFarmMarketingDataModel->getMarketSummaryByBuyer($marketConditions);

            // Format numeric values for crop data
            foreach ($cropData as &$row) {
                $row['min_price'] = (float)$row['min_price'];
                $row['max_price'] = (float)$row['max_price'];
                $row['avg_price'] = (float)$row['avg_price'];
                $row['min_freight'] = (float)$row['min_freight'];
                $row['max_freight'] = (float)$row['max_freight'];
                $row['avg_freight'] = (float)$row['avg_freight'];
                $row['total_quantity'] = (float)$row['total_quantity'];
                $row['total_buyers'] = (int)$row['total_buyers'];
                $row['total_transactions'] = (int)$row['total_transactions'];
                $row['selling_locations'] = explode(',', $row['selling_locations']);
            }

            // Format numeric values for buyer data
            foreach ($buyerData as &$row) {
                $row['transaction_count'] = (int)$row['transaction_count'];
                $row['total_quantity'] = (float)$row['total_quantity'];
                $row['total_value'] = (float)$row['total_value'];
                $row['avg_price'] = (float)$row['avg_price'];
                $row['total_freight_cost'] = (float)$row['total_freight_cost'];
                $row['selling_locations'] = explode(',', $row['selling_locations']);
            }

            return [
                'crops' => $cropData,
                'buyers' => $buyerData
            ];

        } catch (\Exception $e) {
            log_message('error', 'Error in getCropsMarketData: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return [
                'crops' => [],
                'buyers' => []
            ];
        }
    }

    private function getCropsBuyersData($conditions)
    {
        $tableName = 'crops_farm_marketing_data';
        return $this->cropsFarmMarketingDataModel->builder()
            ->select("
                crop_buyers.name as buyer_name,
                COUNT(*) as transaction_count,
                SUM({$tableName}.quantity) as total_quantity,
                {$tableName}.unit_of_measure,
                SUM({$tableName}.market_price_per_unit * {$tableName}.quantity) as total_value,
                SUM({$tableName}.total_freight_cost) as total_freight_cost,
                {$tableName}.selling_location
            ")
            ->join('crop_buyers', "crop_buyers.id = {$tableName}.buyer_id")
            ->where("{$tableName}.status", 'active')
            ->where("{$tableName}.market_date >=", $conditions['created_at >='])
            ->groupBy('crop_buyers.id')
            ->get()->getResultArray();
    }

    private function getFertilizersData($conditions)
    {
        try {
            $tableName = 'crops_farm_fertilizer_data';
            $result = $this->cropsFarmFertilizerDataModel->builder()
                ->select("
                    {$tableName}.fertilizer_id,
                    adx_fertilizers.name,
                    adx_fertilizers.color_code,
                    SUM({$tableName}.quantity) as total_quantity,
                    {$tableName}.unit_of_measure,
                    COUNT(DISTINCT {$tableName}.block_id) as total_blocks,
                    COUNT(*) as total_applications
                ")
                ->join('adx_fertilizers', "adx_fertilizers.id = {$tableName}.fertilizer_id")
                ->join('crops_farm_blocks', "crops_farm_blocks.id = {$tableName}.block_id")
                ->where("{$tableName}.status", 'active')
                ->where('crops_farm_blocks.status', 'active');

            // Apply date filter if exists
            if (isset($conditions['created_at >='])) {
                $result->where("{$tableName}.created_at >=", $conditions['created_at >=']);
            }

            // Apply location filters
            if (!empty($conditions['province_id'])) {
                $result->where('crops_farm_blocks.province_id', $conditions['province_id']);
            }
            if (!empty($conditions['district_id'])) {
                $result->where('crops_farm_blocks.district_id', $conditions['district_id']);
            }
            if (!empty($conditions['llg_id'])) {
                $result->where('crops_farm_blocks.llg_id', $conditions['llg_id']);
            }
            if (!empty($conditions['ward_id'])) {
                $result->where('crops_farm_blocks.ward_id', $conditions['ward_id']);
            }

            $data = $result->groupBy("{$tableName}.fertilizer_id")
                ->get()
                ->getResultArray();

            // Format numeric values
            foreach ($data as &$row) {
                $row['total_quantity'] = (float)$row['total_quantity'];
                $row['total_blocks'] = (int)$row['total_blocks'];
                $row['total_applications'] = (int)$row['total_applications'];
            }

            return $data;

        } catch (\Exception $e) {
            log_message('error', 'Error in getFertilizersData: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return [];
        }
    }

    private function getPesticidesData($conditions)
    {
        try {
            $tableName = 'crops_farm_pesticides_data';
            $result = $this->cropsFarmPesticidesDataModel->builder()
                ->select("
                    {$tableName}.pesticide_id,
                    adx_pesticides.name,
                    SUM({$tableName}.quantity) as total_quantity,
                    {$tableName}.unit_of_measure,
                    COUNT(DISTINCT {$tableName}.block_id) as total_blocks,
                    COUNT(*) as total_applications
                ")
                ->join('adx_pesticides', "adx_pesticides.id = {$tableName}.pesticide_id")
                ->join('crops_farm_blocks', "crops_farm_blocks.id = {$tableName}.block_id")
                ->where("{$tableName}.status", 'active')
                ->where('crops_farm_blocks.status', 'active');

            // Apply date filter if exists
            if (isset($conditions['created_at >='])) {
                $result->where("{$tableName}.created_at >=", $conditions['created_at >=']);
            }

            // Apply location filters
            if (!empty($conditions['province_id'])) {
                $result->where('crops_farm_blocks.province_id', $conditions['province_id']);
            }
            if (!empty($conditions['district_id'])) {
                $result->where('crops_farm_blocks.district_id', $conditions['district_id']);
            }
            if (!empty($conditions['llg_id'])) {
                $result->where('crops_farm_blocks.llg_id', $conditions['llg_id']);
            }
            if (!empty($conditions['ward_id'])) {
                $result->where('crops_farm_blocks.ward_id', $conditions['ward_id']);
            }

            $data = $result->groupBy("{$tableName}.pesticide_id")
                ->get()
                ->getResultArray();

            // Format numeric values
            foreach ($data as &$row) {
                $row['total_quantity'] = (float)$row['total_quantity'];
                $row['total_blocks'] = (int)$row['total_blocks'];
                $row['total_applications'] = (int)$row['total_applications'];
            }

            return $data;

        } catch (\Exception $e) {
            log_message('error', 'Error in getPesticidesData: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return [];
        }
    }

    private function getLivestockMarketData($conditions)
    {
        try {
            $tableName = 'livestock_farm_data';
            $result = $this->livestockFarmDataModel->builder()
                ->select("
                    {$tableName}.livestock_id,
                    adx_livestock.livestock_name,
                    adx_livestock.livestock_color_code,
                    MIN({$tableName}.cost_per_livestock) as min_cost,
                    MAX({$tableName}.cost_per_livestock) as max_cost,
                    AVG({$tableName}.cost_per_livestock) as avg_cost,
                    MIN({$tableName}.low_price_per_livestock) as min_selling_price,
                    MAX({$tableName}.high_price_per_livestock) as max_selling_price,
                    AVG(({$tableName}.low_price_per_livestock + {$tableName}.high_price_per_livestock) / 2) as avg_selling_price,
                    COUNT(*) as total_records
                ")
                ->join('adx_livestock', "adx_livestock.id = {$tableName}.livestock_id")
                ->join('livestock_farm_blocks', "livestock_farm_blocks.id = {$tableName}.block_id")
                ->where("{$tableName}.status", 'active')
                ->where('livestock_farm_blocks.status', 'active');

            // Apply date filter if exists
            if (isset($conditions['created_at >='])) {
                $result->where("{$tableName}.created_at >=", $conditions['created_at >=']);
            }

            // Apply location filters
            if (!empty($conditions['province_id'])) {
                $result->where('livestock_farm_blocks.province_id', $conditions['province_id']);
            }
            if (!empty($conditions['district_id'])) {
                $result->where('livestock_farm_blocks.district_id', $conditions['district_id']);
            }
            if (!empty($conditions['llg_id'])) {
                $result->where('livestock_farm_blocks.llg_id', $conditions['llg_id']);
            }
            if (!empty($conditions['ward_id'])) {
                $result->where('livestock_farm_blocks.ward_id', $conditions['ward_id']);
            }

            $data = $result->groupBy("{$tableName}.livestock_id")
                ->get()
                ->getResultArray();

            // Format numeric values
            foreach ($data as &$row) {
                $row['min_cost'] = (float)$row['min_cost'];
                $row['max_cost'] = (float)$row['max_cost'];
                $row['avg_cost'] = (float)$row['avg_cost'];
                $row['min_selling_price'] = (float)$row['min_selling_price'];
                $row['max_selling_price'] = (float)$row['max_selling_price'];
                $row['avg_selling_price'] = (float)$row['avg_selling_price'];
                $row['total_records'] = (int)$row['total_records'];
            }

            return $data;

        } catch (\Exception $e) {
            log_message('error', 'Error in getLivestockMarketData: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return [];
        }
    }

    private function applyConditions($builder, $conditions)
    {
        // Get the table name from the builder
        $tableName = $builder->getTable();
        
        // Apply location filters progressively
        if (isset($conditions['province_id']) && $conditions['province_id'] !== '') {
            $builder->where($tableName . '.province_id', $conditions['province_id']);
        }
        if (isset($conditions['district_id']) && $conditions['district_id'] !== '') {
            $builder->where($tableName . '.district_id', $conditions['district_id']);
        }
        if (isset($conditions['llg_id']) && $conditions['llg_id'] !== '') {
            $builder->where($tableName . '.llg_id', $conditions['llg_id']);
        }
        if (isset($conditions['ward_id']) && $conditions['ward_id'] !== '') {
            $builder->where($tableName . '.ward_id', $conditions['ward_id']);
        }
        
        // Always apply date filter
        if (isset($conditions['created_at >='])) {
            $builder->where($tableName . '.created_at >=', $conditions['created_at >=']);
        }
    }
}
