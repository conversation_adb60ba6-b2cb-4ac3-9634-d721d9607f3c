<?php

namespace App\Controllers\Staff;

use App\Controllers\BaseController;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel as FarmBlockModel;
use App\Models\CropsFarmCropsDataModel as FarmCropsDataModel;
use App\Models\CropsFarmFertilizerDataModel as FarmFertilizerDataModel;
use App\Models\CropsFarmPesticidesDataModel as FarmPesticidesDataModel;
use App\Models\CropsFarmHarvestDataModel as FarmHarvestDataModel;
use App\Models\CropsFarmMarketingDataModel as FarmMarketingDataModel;
use App\Models\CropsFarmDiseaseDataModel as FarmDiseaseDataModel;
use App\Models\CropsModel;
use App\Models\CropBuyersModel;
use App\Models\provinceModel;
use App\Models\districtModel;
use App\Models\llgModel;
use App\Models\wardModel;
use App\Models\InfectionsModel;
use App\Models\PesticidesModel;
use App\Models\FertilizersModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\LivestockFarmDataModel;
use App\Models\LivestockModel;

class Staff_Reports extends BaseController
{
    protected $farmersModel;
    protected $farmBlockModel;
    protected $farmCropsDataModel;
    protected $farmDiseaseDataModel;
    protected $farmFertilizerDataModel;
    protected $farmPesticidesDataModel;
    protected $farmHarvestDataModel;
    protected $farmMarketingDataModel;
    protected $cropsModel;
    protected $infectionsModel;
    protected $pesticidesModel;
    protected $fertilizersModel;
    protected $cropBuyersModel;
    protected $livestockFarmBlockModel;
    protected $livestockFarmDataModel;
    protected $livestockModel;

    public function __construct()
    {
        helper(['url', 'form', 'info']);
        $this->farmersModel = new FarmerInformationModel();
        $this->farmBlockModel = new FarmBlockModel();
        $this->farmCropsDataModel = new FarmCropsDataModel();
        $this->farmDiseaseDataModel = new FarmDiseaseDataModel();
        $this->farmFertilizerDataModel = new FarmFertilizerDataModel();
        $this->farmPesticidesDataModel = new FarmPesticidesDataModel();
        $this->farmHarvestDataModel = new FarmHarvestDataModel();
        $this->farmMarketingDataModel = new FarmMarketingDataModel();
        $this->cropsModel = new CropsModel();
        $this->infectionsModel = new InfectionsModel();
        $this->pesticidesModel = new PesticidesModel();
        $this->fertilizersModel = new FertilizersModel();
        $this->cropBuyersModel = new CropBuyersModel();
        $this->livestockFarmBlockModel = new \App\Models\LivestockFarmBlockModel();
        $this->livestockFarmDataModel = new \App\Models\LivestockFarmDataModel();
        $this->livestockModel = new \App\Models\LivestockModel();
    }

    public function farmers()
    {
        $farmers = $this->getFarmersWithDetails();

        // Get crop distribution by LLG
        $cropDistribution = $this->getCropDistributionByLLG();

        $data = [
            'title' => 'Farmers Reports',
            'page_header' => 'Farmers Reports',
            'farmers' => $farmers,
            'crop_distribution' => $cropDistribution,
            'stats' => [
                'total_farmers' => count($farmers),
                'active_farmers' => count(array_filter($farmers, fn($f) => $f['status'] === 'active')),
                'inactive_farmers' => count(array_filter($farmers, fn($f) => $f['status'] === 'inactive'))
            ]
        ];

        return view('staff/reports/farmers', $data);
    }

    private function getFarmersWithDetails()
    {
        $farmers = $this->farmersModel
            ->select('farmer_information.*,
                     adx_llg.name as llg_name,
                     adx_ward.name as ward_name,
                     (SELECT COUNT(*) FROM farmers_children
                      WHERE farmers_children.farmer_id = farmer_information.id) as children_count,
                     (SELECT COUNT(*) FROM crops_farm_blocks
                      WHERE crops_farm_blocks.farmer_id = farmer_information.id
                      AND crops_farm_blocks.status = "active") as farm_blocks_count')
            ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id', 'left')
            ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id', 'left')
            ->where('farmer_information.district_id', session()->get('district_id'))
            ->where('farmer_information.status', 'active')
            ->findAll();

        return $farmers;
    }

    private function getCropDistributionByLLG()
    {
        $db = \Config\Database::connect();

        $query = $db->query("
            SELECT
                llg.name as llg_name,
                crops.crop_name,
                crops.crop_color_code,
                COUNT(DISTINCT cfb.farmer_id) as farmer_count
            FROM adx_llg llg
            LEFT JOIN crops_farm_blocks cfb ON cfb.llg_id = llg.id AND cfb.status = 'active'
            LEFT JOIN adx_crops crops ON crops.id = cfb.crop_id
            WHERE llg.district_id = ?
            GROUP BY llg.id, llg.name, crops.id, crops.crop_name, crops.crop_color_code
            ORDER BY llg.name, crops.crop_name
        ", [session()->get('district_id')]);

        $distribution = [];
        $cropTypes = [];

        foreach ($query->getResultArray() as $row) {
            if ($row['crop_name']) {
                $distribution[$row['llg_name']][$row['crop_name']] = $row['farmer_count'];
                if (!in_array($row['crop_name'], $cropTypes)) {
                    $cropTypes[] = $row['crop_name'];
                }
            }
        }

        return [
            'distribution' => $distribution,
            'crop_types' => $cropTypes
        ];
    }

    public function crops()
    {
        $data = [
            'title' => 'Crops Reports',
            'page_header' => 'Crops Reports',
            'crops_data' => $this->farmCropsDataModel->getCropsReportData(),
            'crops' => $this->cropsModel->findAll()
        ];

        return view('staff/reports/crops', $data);
    }

    public function blocks()
    {
        $farmBlockModel = new FarmBlockModel();
        $blocks_data = $farmBlockModel->getFarmBlocksWithDetails();

        // Get statistics for blocks
        $stats = [
            'total_blocks' => count($blocks_data),
            'total_active' => count(array_filter($blocks_data, fn($b) => $b['status'] === 'active')),
            'by_llg' => [],
            'by_ward' => [],
            'by_crop' => []
        ];

        // Process statistics and crop colors
        $cropColors = [];
        foreach ($blocks_data as $block) {
            // Count by crop
            if (!isset($stats['by_crop'][$block['crop_name']])) {
                $stats['by_crop'][$block['crop_name']] = 0;
                $cropColors[$block['crop_name']] = $block['crop_color_code'] ?? '#' . substr(md5($block['crop_name']), 0, 6);
            }
            $stats['by_crop'][$block['crop_name']]++;

            // Count by LLG
            if (!isset($stats['by_llg'][$block['llg_name']])) {
                $stats['by_llg'][$block['llg_name']] = 0;
            }
            $stats['by_llg'][$block['llg_name']]++;

            // Count by Ward
            if (!isset($stats['by_ward'][$block['ward_name']])) {
                $stats['by_ward'][$block['ward_name']] = 0;
            }
            $stats['by_ward'][$block['ward_name']]++;
        }

        $data = [
            'title' => 'Farm Blocks Reports',
            'page_header' => 'Farm Blocks Reports',
            'blocks_data' => $blocks_data,
            'stats' => $stats,
            'cropColors' => $cropColors
        ];

        return view('staff/reports/blocks', $data);
    }

    public function diseases()
    {
        // Get diseases data with active blocks only
        $diseases_data = $this->farmDiseaseDataModel->getDiseasesReportData();

        // Filter for active blocks only
        $diseases_data = array_filter($diseases_data, function($record) {
            return isset($record['block_status']) && $record['block_status'] === 'active';
        });

        // Reset array keys after filtering
        $diseases_data = array_values($diseases_data);

        // Process statistics
        $stats = [
            'total_cases' => count($diseases_data),
            'total_plants_affected' => array_sum(array_column($diseases_data, 'number_of_plants')),
            'total_hectares_affected' => array_sum(array_column($diseases_data, 'hectares')),
            'by_disease' => [],
            'by_crop' => [],
            'by_district' => [],
            'monthly_cases' => array_fill(0, 12, 0)
        ];

        foreach ($diseases_data as $case) {
            // Count by disease type
            if (!isset($stats['by_disease'][$case['disease_name']])) {
                $stats['by_disease'][$case['disease_name']] = [
                    'count' => 0,
                    'plants' => 0,
                    'hectares' => 0
                ];
            }
            $stats['by_disease'][$case['disease_name']]['count']++;
            $stats['by_disease'][$case['disease_name']]['plants'] += $case['number_of_plants'];
            $stats['by_disease'][$case['disease_name']]['hectares'] += $case['hectares'];

            // Count by crop
            if (!isset($stats['by_crop'][$case['crop_name']])) {
                $stats['by_crop'][$case['crop_name']] = [
                    'count' => 0,
                    'color' => $case['crop_color_code']
                ];
            }
            $stats['by_crop'][$case['crop_name']]['count']++;

            // Count by district
            if (!isset($stats['by_district'][$case['district_name']])) {
                $stats['by_district'][$case['district_name']] = 0;
            }
            $stats['by_district'][$case['district_name']]++;

            // Count monthly cases
            $month = date('n', strtotime($case['action_date'])) - 1;
            $stats['monthly_cases'][$month]++;
        }

        $data = [
            'title' => 'Diseases Reports',
            'page_header' => 'Diseases Reports',
            'diseases_data' => $diseases_data,
            'stats' => $stats
        ];

        return view('staff/reports/diseases', $data);
    }

    public function fertilizer()
    {
        // Get fertilizer data with active blocks only
        $fertilizer_data = $this->farmFertilizerDataModel->getFertilizerReportData();

        // Filter for active blocks only
        $fertilizer_data = array_filter($fertilizer_data, function($record) {
            return isset($record['block_status']) && $record['block_status'] === 'active';
        });

        // Reset array keys after filtering
        $fertilizer_data = array_values($fertilizer_data);

        $data = [
            'title' => 'Fertilizer Reports',
            'page_header' => 'Fertilizer Reports',
            'fertilizer_data' => $fertilizer_data,
            'fertilizers' => $this->fertilizersModel->findAll()
        ];

        return view('staff/reports/fertilizer', $data);
    }

    public function pesticides()
    {
        // Get pesticides data with active blocks only
        $pesticides_data = $this->farmPesticidesDataModel->getPesticidesReportData();

        // Filter for active blocks only
        $pesticides_data = array_filter($pesticides_data, function($record) {
            return isset($record['block_status']) && $record['block_status'] === 'active';
        });

        // Reset array keys after filtering
        $pesticides_data = array_values($pesticides_data);

        $data = [
            'title' => 'Pesticides Reports',
            'page_header' => 'Pesticides Reports',
            'pesticides_data' => $pesticides_data,
            'pesticides' => $this->pesticidesModel->findAll()
        ];

        return view('staff/reports/pesticides', $data);
    }

    public function harvests()
    {
        // Get harvest data with active blocks only
        $harvest_data = $this->farmHarvestDataModel->getHarvestReportData();

        // Filter for active blocks only
        $harvest_data = array_filter($harvest_data, function($record) {
            return isset($record['block_status']) && $record['block_status'] === 'active';
        });

        // Reset array keys after filtering
        $harvest_data = array_values($harvest_data);

        // Process statistics
        $stats = [
            'total_harvests' => count($harvest_data),
            'total_quantity' => array_sum(array_column($harvest_data, 'quantity')),
            'by_crop' => [],
            'by_district' => [],
            'monthly_harvests' => array_fill(0, 12, 0),
            'monthly_quantity' => array_fill(0, 12, 0)
        ];

        foreach ($harvest_data as $harvest) {
            // Stats by crop
            if (!isset($stats['by_crop'][$harvest['crop_name']])) {
                $stats['by_crop'][$harvest['crop_name']] = [
                    'quantity' => 0,
                    'harvests' => 0,
                    'color' => $harvest['crop_color_code']
                ];
            }
            $stats['by_crop'][$harvest['crop_name']]['quantity'] += $harvest['quantity'];
            $stats['by_crop'][$harvest['crop_name']]['harvests']++;

            // Stats by district
            if (!isset($stats['by_district'][$harvest['district_name']])) {
                $stats['by_district'][$harvest['district_name']] = [
                    'quantity' => 0,
                    'harvests' => 0
                ];
            }
            $stats['by_district'][$harvest['district_name']]['quantity'] += $harvest['quantity'];
            $stats['by_district'][$harvest['district_name']]['harvests']++;

            // Monthly stats
            $month = date('n', strtotime($harvest['harvest_date'])) - 1;
            $stats['monthly_harvests'][$month]++;
            $stats['monthly_quantity'][$month] += $harvest['quantity'];
        }

        // Sort arrays by quantity
        arsort($stats['by_crop']);
        arsort($stats['by_district']);

        $data = [
            'title' => 'Harvest Reports',
            'page_header' => 'Harvest Reports',
            'harvest_data' => $harvest_data,
            'stats' => $stats
        ];

        return view('staff/reports/harvests', $data);
    }

    public function marketing()
    {
        // Get marketing data
        $conditions = [];

        // Only filter by district if it's set in the session
        if (session()->has('district_id') && session()->get('district_id') > 0) {
            $conditions['crops_farm_marketing_data.district_id'] = session()->get('district_id');
        }

        $marketing_data = $this->farmMarketingDataModel->getMarketingReportData($conditions);

        // Log the number of records found
        log_message('debug', 'Marketing Report: Found ' . count($marketing_data) . ' records');

        // Process statistics
        $stats = [
            'total_transactions' => count($marketing_data),
            'total_revenue' => 0,
            'total_quantity' => 0,
            'by_crop' => [],
            'by_buyer' => [],
            'monthly_revenue' => array_fill(0, 12, 0)
        ];

        // Group data by farmer
        $farmer_totals = [];
        foreach ($marketing_data as $transaction) {
            $farmer_name = $transaction['given_name'] . ' ' . $transaction['surname'];
            $quantity = floatval($transaction['quantity']);
            $revenue = $quantity * floatval($transaction['market_price_per_unit']);
            $freight_cost = floatval($transaction['total_freight_cost'] ?? 0);

            if (!isset($farmer_totals[$farmer_name])) {
                $farmer_totals[$farmer_name] = [
                    'farmer_name' => $farmer_name,
                    'total_revenue' => 0,
                    'total_freight_cost' => 0,
                    'total_quantity' => 0,
                    'llg_name' => $transaction['llg_name'],
                    'crops' => []
                ];
            }

            // Update farmer totals
            $farmer_totals[$farmer_name]['total_revenue'] += $revenue;
            $farmer_totals[$farmer_name]['total_freight_cost'] += $freight_cost;
            $farmer_totals[$farmer_name]['total_quantity'] += $quantity;

            // Track crops for this farmer
            if (!in_array($transaction['crop_name'], $farmer_totals[$farmer_name]['crops'])) {
                $farmer_totals[$farmer_name]['crops'][] = $transaction['crop_name'];
            }

            // Update overall stats
            $stats['total_revenue'] += $revenue;
            $stats['total_quantity'] += floatval($transaction['quantity']);

            // Stats by crop
            if (!isset($stats['by_crop'][$transaction['crop_name']])) {
                $stats['by_crop'][$transaction['crop_name']] = [
                    'revenue' => 0,
                    'color' => $transaction['crop_color_code']
                ];
            }
            $stats['by_crop'][$transaction['crop_name']]['revenue'] += $revenue;

            // Stats by buyer
            $buyer_name = $transaction['buyer_name'] ?? 'Unknown';
            if (!isset($stats['by_buyer'][$buyer_name])) {
                $stats['by_buyer'][$buyer_name] = [
                    'revenue' => 0
                ];
            }
            $stats['by_buyer'][$buyer_name]['revenue'] += $revenue;

            // Monthly stats
            $month = date('n', strtotime($transaction['market_date'])) - 1;
            $stats['monthly_revenue'][$month] += $revenue;
        }

        // Sort farmers by total revenue
        uasort($farmer_totals, function($a, $b) {
            return $b['total_revenue'] <=> $a['total_revenue'];
        });

        $data = [
            'title' => 'Marketing Reports',
            'page_header' => 'Marketing Reports',
            'farmer_totals' => $farmer_totals,
            'stats' => $stats
        ];

        return view('staff/reports/marketing', $data);
    }

    public function livestock_blocks()
    {
        try {
            // Get livestock colors from the model property
            $livestock_colors = [];
            foreach ($this->livestockModel->findAll() as $livestock) {
                $livestock_colors[$livestock['livestock_name']] = $livestock['livestock_color_code'];
            }

            // Fetch livestock farm blocks data with necessary joins including livestock types
            $blocks_data = $this->livestockFarmBlockModel
                ->select('livestock_farm_blocks.*,
                          farmer_information.given_name,
                          farmer_information.surname,
                          ward.name as ward_name,
                          llg.name as llg_name,
                          district.name as district_name,
                          province.name as province_name,
                          GROUP_CONCAT(DISTINCT adx_livestock.livestock_name) as livestock_types,
                          GROUP_CONCAT(DISTINCT adx_livestock.livestock_color_code) as livestock_colors')
                ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
                ->join('adx_ward ward', 'ward.id = livestock_farm_blocks.ward_id', 'left')
                ->join('adx_llg llg', 'llg.id = livestock_farm_blocks.llg_id', 'left')
                ->join('adx_district district', 'district.id = livestock_farm_blocks.district_id', 'left')
                ->join('adx_province province', 'province.id = livestock_farm_blocks.province_id', 'left')
                ->join('livestock_farm_data', 'livestock_farm_data.block_id = livestock_farm_blocks.id', 'left')
                ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id', 'left')
                ->where('livestock_farm_blocks.district_id', session()->get('district_id'))
                ->where('livestock_farm_blocks.status !=', 'deleted')
                ->groupBy('livestock_farm_blocks.id')
                ->orderBy('livestock_farm_blocks.id', 'ASC')
                ->findAll();

            if (empty($blocks_data)) {
                log_message('warning', '[Livestock Blocks Report] No livestock farm blocks found');
                $data = [
                    'title' => 'Livestock Farm Blocks Reports',
                    'page_header' => 'Livestock Farm Blocks Reports',
                    'blocks_data' => [],
                    'stats' => [
                        'total_blocks' => 0,
                        'total_active' => 0,
                        'total_inactive' => 0,
                        'by_district' => [],
                        'by_llg' => [],
                        'by_ward' => [],
                        'by_livestock_type' => [],
                        'livestock_colors' => []
                    ]
                ];
                return view('staff/reports/livestock_blocks', $data);
            }

            // Process statistics
            $stats = [
                'total_blocks' => count($blocks_data),
                'total_active' => count(array_filter($blocks_data, fn($b) => $b['status'] === 'active')),
                'total_inactive' => count(array_filter($blocks_data, fn($b) => $b['status'] === 'inactive')),
                'by_llg' => [],
                'by_livestock_type' => [],
                'livestock_colors' => $livestock_colors
            ];

            foreach ($blocks_data as $block) {
                // Process LLG stats
                if (!empty($block['llg_name'])) {
                    if (!isset($stats['by_llg'][$block['llg_name']])) {
                        $stats['by_llg'][$block['llg_name']] = 0;
                    }
                    $stats['by_llg'][$block['llg_name']]++;
                }

                // Process livestock type stats
                if (!empty($block['livestock_types'])) {
                    $types = explode(',', $block['livestock_types']);
                    foreach ($types as $type) {
                        if (!isset($stats['by_livestock_type'][$type])) {
                            $stats['by_livestock_type'][$type] = 0;
                        }
                        $stats['by_livestock_type'][$type]++;
                    }
                }
            }

            $data = [
                'title' => 'Livestock Farm Blocks Reports',
                'page_header' => 'Livestock Farm Blocks Reports',
                'blocks_data' => $blocks_data,
                'stats' => $stats
            ];

            return view('staff/reports/livestock_blocks', $data);

        } catch (\Exception $e) {
            log_message('error', '[Livestock Blocks Report] ' . $e->getMessage());
            $data = [
                'title' => 'Livestock Farm Blocks Reports',
                'page_header' => 'Livestock Farm Blocks Reports',
                'blocks_data' => [],
                'stats' => [
                    'total_blocks' => 0,
                    'total_active' => 0,
                    'total_inactive' => 0,
                    'by_district' => [],
                    'by_llg' => [],
                    'by_ward' => [],
                    'by_livestock_type' => [],
                    'livestock_colors' => []
                ],
                'error' => 'Failed to retrieve livestock blocks report'
            ];
            return view('staff/reports/livestock_blocks', $data);
        }
    }

    public function livestock_data()
    {
        // Get livestock farm data with details
        $livestock_data = $this->livestockFarmDataModel
            ->select('livestock_farm_data.*, livestock_farm_blocks.block_code, livestock_farm_blocks.status as block_status,
                    farmer_information.given_name, farmer_information.surname,
                    adx_livestock.livestock_name,
                    adx_llg.name as llg_name')
            ->join('livestock_farm_blocks', 'livestock_farm_blocks.id = livestock_farm_data.block_id')
            ->join('farmer_information', 'farmer_information.id = livestock_farm_blocks.farmer_id')
            ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id')
            ->join('adx_llg', 'adx_llg.id = livestock_farm_blocks.llg_id')
            ->where('livestock_farm_blocks.district_id', session()->get('district_id'))
            ->where('livestock_farm_data.status !=', 'deleted')
            ->findAll();

        // Process statistics
        $stats = [
            'total_records' => count($livestock_data),
            'total_male' => array_sum(array_column($livestock_data, 'he_total')),
            'total_female' => array_sum(array_column($livestock_data, 'she_total')),
            'by_llg' => [],
            'monthly_data' => array_fill(0, 12, ['count' => 0, 'total' => 0])
        ];

        foreach ($livestock_data as $record) {
            $total = $record['he_total'] + $record['she_total'];

            // Stats by LLG
            if (!isset($stats['by_llg'][$record['llg_name']])) {
                $stats['by_llg'][$record['llg_name']] = [
                    'total' => 0,
                    'count' => 0,
                    'total_cost' => 0,
                    'total_low_price' => 0,
                    'total_high_price' => 0,
                    'min_cost' => PHP_FLOAT_MAX,
                    'max_cost' => 0,
                    'min_low_price' => PHP_FLOAT_MAX,
                    'max_low_price' => 0,
                    'min_high_price' => PHP_FLOAT_MAX,
                    'max_high_price' => 0,
                    'by_livestock_type' => []
                ];
            }

            // Initialize livestock type stats for this LLG if not exists
            if (!isset($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']])) {
                $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']] = [
                    'total' => 0,
                    'count' => 0,
                    'total_cost' => 0,
                    'total_low_price' => 0,
                    'total_high_price' => 0,
                    'min_cost' => PHP_FLOAT_MAX,
                    'max_cost' => 0,
                    'min_low_price' => PHP_FLOAT_MAX,
                    'max_low_price' => 0,
                    'min_high_price' => PHP_FLOAT_MAX,
                    'max_high_price' => 0
                ];
            }

            // Cost statistics
            $cost = floatval($record['cost_per_livestock']);
            $low_price = floatval($record['low_price_per_livestock']);
            $high_price = floatval($record['high_price_per_livestock']);

            // Update LLG totals
            $stats['by_llg'][$record['llg_name']]['total'] += $total;
            $stats['by_llg'][$record['llg_name']]['count']++;
            $stats['by_llg'][$record['llg_name']]['total_cost'] += $cost;
            $stats['by_llg'][$record['llg_name']]['total_low_price'] += $low_price;
            $stats['by_llg'][$record['llg_name']]['total_high_price'] += $high_price;

            // Update min/max values for LLG
            $stats['by_llg'][$record['llg_name']]['min_cost'] = min($stats['by_llg'][$record['llg_name']]['min_cost'], $cost);
            $stats['by_llg'][$record['llg_name']]['max_cost'] = max($stats['by_llg'][$record['llg_name']]['max_cost'], $cost);
            $stats['by_llg'][$record['llg_name']]['min_low_price'] = min($stats['by_llg'][$record['llg_name']]['min_low_price'], $low_price);
            $stats['by_llg'][$record['llg_name']]['max_low_price'] = max($stats['by_llg'][$record['llg_name']]['max_low_price'], $low_price);
            $stats['by_llg'][$record['llg_name']]['min_high_price'] = min($stats['by_llg'][$record['llg_name']]['min_high_price'], $high_price);
            $stats['by_llg'][$record['llg_name']]['max_high_price'] = max($stats['by_llg'][$record['llg_name']]['max_high_price'], $high_price);

            // Update livestock type stats for this LLG
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['total'] += $total;
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['count']++;
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['total_cost'] += $cost;
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['total_low_price'] += $low_price;
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['total_high_price'] += $high_price;

            // Update min/max values for livestock type in this LLG
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_cost'] =
                min($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_cost'], $cost);
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_cost'] =
                max($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_cost'], $cost);
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_low_price'] =
                min($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_low_price'], $low_price);
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_low_price'] =
                max($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_low_price'], $low_price);
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_high_price'] =
                min($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['min_high_price'], $high_price);
            $stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_high_price'] =
                max($stats['by_llg'][$record['llg_name']]['by_livestock_type'][$record['livestock_name']]['max_high_price'], $high_price);
        }

        $data = [
            'title' => 'Livestock Farm Data Reports',
            'page_header' => 'Livestock Farm Data Reports',
            'livestock_data' => $livestock_data,
            'stats' => $stats
        ];

        return view('staff/reports/livestock_data', $data);
    }

    public function farmer_profile($farmer_id = null)
    {
        if (!$farmer_id) {
            return redirect()->to('/staff/reports/farmers');
        }

        // Get basic farmer information
        $farmer = $this->farmersModel->select('
            farmer_information.*,
            adx_llg.name as llg_name,
            adx_ward.name as ward_name,
            adx_district.name as district_name,
            adx_province.name as province_name,
            (SELECT COUNT(*) FROM farmers_children WHERE farmer_id = farmer_information.id) as children_count
        ')
        ->join('adx_llg', 'adx_llg.id = farmer_information.llg_id', 'left')
        ->join('adx_ward', 'adx_ward.id = farmer_information.ward_id', 'left')
        ->join('adx_district', 'adx_district.id = farmer_information.district_id', 'left')
        ->join('adx_province', 'adx_province.id = farmer_information.province_id', 'left')
        ->where('farmer_information.id', $farmer_id)
        ->where('farmer_information.status', 'active')
        ->first();

        if (!$farmer) {
            return redirect()->to('/staff/reports/farmers');
        }

        // Get children information
        $children = (new \App\Models\FarmersChildrenModel())
            ->where('farmer_id', $farmer_id)
            ->findAll();

        // Get farm blocks information
        $farmBlocks = $this->farmBlockModel
            ->select('crops_farm_blocks.*, adx_crops.crop_name, adx_crops.crop_color_code,
                     (SELECT SUM(hectares) FROM crops_farm_crops_data
                      WHERE crops_farm_crops_data.block_id = crops_farm_blocks.id
                      AND crops_farm_crops_data.status = "active") as total_hectares')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
            ->where('crops_farm_blocks.farmer_id', $farmer_id)
            ->where('crops_farm_blocks.status', 'active')
            ->findAll();

        // Get livestock blocks
        $livestockBlocks = $this->livestockFarmBlockModel
            ->where('farmer_id', $farmer_id)
            ->where('livestock_farm_blocks.status', 'active')
            ->findAll();

        // Get last 12 months data
        $twelveMonthsAgo = date('Y-m-d', strtotime('-12 months'));

        // Get harvests data
        $harvests = $this->farmHarvestDataModel
            ->select('
                crops_farm_harvest_data.*,
                crops_farm_blocks.block_code,
                adx_crops.crop_name,
                adx_crops.crop_color_code
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_harvest_data.block_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_harvest_data.crop_id')
            ->where('crops_farm_blocks.farmer_id', $farmer_id)
            ->where('crops_farm_harvest_data.status', 'active')
            ->where('crops_farm_blocks.status', 'active')
            ->where('harvest_date >=', $twelveMonthsAgo)
            ->orderBy('harvest_date', 'DESC')
            ->findAll();

        // Get marketing data
        $marketing = $this->farmMarketingDataModel
            ->select('
                crops_farm_marketing_data.*,
                adx_crops.crop_name,
                crop_buyers.name as buyer_name
            ')
            ->join('adx_crops', 'adx_crops.id = crops_farm_marketing_data.crop_id')
            ->join('crop_buyers', 'crop_buyers.id = crops_farm_marketing_data.buyer_id', 'left')
            ->where('crops_farm_marketing_data.farmer_id', $farmer_id)
            ->where('crops_farm_marketing_data.status', 'active')
            ->where('market_date >=', $twelveMonthsAgo)
            ->orderBy('market_date', 'DESC')
            ->findAll();

        // Get fertilizer usage
        $fertilizers = $this->farmFertilizerDataModel
            ->select('
                crops_farm_fertilizer_data.*,
                crops_farm_blocks.block_code,
                adx_crops.crop_name,
                adx_fertilizers.name as fertilizer_name
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_fertilizer_data.block_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_fertilizer_data.crop_id')
            ->join('adx_fertilizers', 'adx_fertilizers.id = crops_farm_fertilizer_data.fertilizer_id', 'left')
            ->where('crops_farm_blocks.farmer_id', $farmer_id)
            ->where('crops_farm_fertilizer_data.status', 'active')
            ->where('crops_farm_blocks.status', 'active')
            ->where('action_date >=', $twelveMonthsAgo)
            ->orderBy('action_date', 'DESC')
            ->findAll();

        // Get pesticides usage
        $pesticides = $this->farmPesticidesDataModel
            ->select('
                crops_farm_pesticides_data.*,
                crops_farm_blocks.block_code,
                adx_crops.crop_name,
                adx_pesticides.name as pesticide_name
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_pesticides_data.block_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_pesticides_data.crop_id')
            ->join('adx_pesticides', 'adx_pesticides.id = crops_farm_pesticides_data.pesticide_id', 'left')
            ->where('crops_farm_blocks.farmer_id', $farmer_id)
            ->where('crops_farm_pesticides_data.status', 'active')
            ->where('crops_farm_blocks.status', 'active')
            ->where('action_date >=', $twelveMonthsAgo)
            ->orderBy('action_date', 'DESC')
            ->findAll();

        // Get livestock data
        $livestock = $this->livestockFarmDataModel
            ->select('
                livestock_farm_data.*,
                livestock_farm_blocks.block_code,
                adx_livestock.livestock_name,
                adx_livestock.livestock_color_code
            ')
            ->join('livestock_farm_blocks', 'livestock_farm_blocks.id = livestock_farm_data.block_id')
            ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id')
            ->where('livestock_farm_blocks.farmer_id', $farmer_id)
            ->where('livestock_farm_data.status', 'active')
            ->where('livestock_farm_blocks.status', 'active')
            ->where('action_date >=', $twelveMonthsAgo)
            ->orderBy('action_date', 'DESC')
            ->findAll();

        // Get diseases data
        $diseases = $this->farmDiseaseDataModel
            ->select('
                crops_farm_disease_data.*,
                adx_infections.name as infection_name,
                adx_infections.color_code as infection_color_code,
                crops_farm_blocks.block_code,
                adx_crops.crop_name
            ')
            ->join('adx_infections', 'adx_infections.name = crops_farm_disease_data.disease_name', 'left')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_disease_data.block_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_disease_data.crop_id', 'left')
            ->where('crops_farm_blocks.farmer_id', $farmer_id)
            ->where('crops_farm_disease_data.status', 'active')
            ->where('crops_farm_blocks.status', 'active')
            ->orderBy('crops_farm_disease_data.action_date', 'DESC')
            ->findAll();

        // Calculate summaries
        $summaries = [
            'total_blocks' => count($farmBlocks),
            'total_livestock_blocks' => count($livestockBlocks),
            'total_harvests' => count($harvests),
            'total_marketing' => count($marketing),
            'total_fertilizer_applications' => count($fertilizers),
            'total_pesticide_applications' => count($pesticides),
            'total_livestock' => array_sum(array_map(function($l) {
                return $l['he_total'] + $l['she_total'];
            }, $livestock)),
            'total_disease_cases' => count($diseases),
            'total_revenue' => array_sum(array_map(function($m) {
                return $m['quantity'] * $m['market_price_per_unit'];
            }, $marketing))
        ];

        // Prepare data for view
        $data = [
            'title' => 'Farmer Profile',
            'page_header' => 'Farmer Profile',
            'farmer' => $farmer,
            'children' => $children,
            'farm_blocks' => $farmBlocks,
            'livestock_blocks' => $livestockBlocks,
            'harvests' => $harvests,
            'marketing' => $marketing,
            'fertilizers' => $fertilizers,
            'pesticides' => $pesticides,
            'livestock' => $livestock,
            'diseases' => $diseases,
            'summaries' => $summaries
        ];

        return view('staff/reports/farmers_profile', $data);
    }

    public function block_profile($block_id = null)
    {
        if (!$block_id) {
            return redirect()->to('/staff/reports/blocks');
        }

        // Get basic block information
        $block = $this->farmBlockModel
            ->select('
                crops_farm_blocks.*,
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_crops.crop_color_code,
                adx_llg.name as llg_name,
                adx_ward.name as ward_name
            ')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
            ->where('crops_farm_blocks.id', $block_id)
            ->first();

        if (!$block) {
            return redirect()->to('/staff/reports/blocks');
        }

        // Get crops data
        $crops_data = $this->farmCropsDataModel
            ->select('
                crops_farm_crops_data.*,
                crops_farm_blocks.block_code,
                adx_crops.crop_name
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_crops_data.block_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_crops_data.crop_id')
            ->where('crops_farm_crops_data.block_id', $block_id)
            ->where('crops_farm_crops_data.status', 'active')
            ->orderBy('crops_farm_crops_data.action_date', 'DESC')
            ->findAll();

        // Get diseases data
        $diseases_data = $this->farmDiseaseDataModel
            ->select('
                crops_farm_disease_data.*,
                adx_infections.name as infection_name,
                adx_infections.color_code as infection_color_code
            ')
            ->join('adx_infections', 'adx_infections.name = crops_farm_disease_data.disease_name', 'left')
            ->where('crops_farm_disease_data.block_id', $block_id)
            ->where('crops_farm_disease_data.status', 'active')
            ->orderBy('crops_farm_disease_data.action_date', 'DESC')
            ->findAll();

        // Get fertilizer data
        $fertilizer_data = $this->farmFertilizerDataModel
            ->select('crops_farm_fertilizer_data.*, adx_fertilizers.name as fertilizer_name')
            ->join('adx_fertilizers', 'adx_fertilizers.id = crops_farm_fertilizer_data.fertilizer_id', 'left')
            ->where('block_id', $block_id)
            ->where('crops_farm_fertilizer_data.status', 'active')
            ->orderBy('action_date', 'DESC')
            ->findAll();

        // Get pesticides data
        $pesticides_data = $this->farmPesticidesDataModel
            ->select('crops_farm_pesticides_data.*, adx_pesticides.name as pesticide_name')
            ->join('adx_pesticides', 'adx_pesticides.id = crops_farm_pesticides_data.pesticide_id', 'left')
            ->where('block_id', $block_id)
            ->where('crops_farm_pesticides_data.status', 'active')
            ->orderBy('action_date', 'DESC')
            ->findAll();

        // Get harvest data
        $harvest_data = $this->farmHarvestDataModel
            ->where('block_id', $block_id)
            ->where('status', 'active')
            ->orderBy('harvest_date', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Farm Block Profile - ' . $block['block_code'],
            'page_header' => 'Farm Block Profile',
            'block' => $block,
            'crops_data' => $crops_data,
            'diseases_data' => $diseases_data,
            'fertilizer_data' => $fertilizer_data,
            'pesticides_data' => $pesticides_data,
            'harvest_data' => $harvest_data
        ];

        return view('staff/reports/blocks_profile', $data);
    }

    // Helper method to export reports to Excel/PDF if needed
    protected function exportReport($data, $type)
    {
        // Implementation for exporting reports
    }
}
